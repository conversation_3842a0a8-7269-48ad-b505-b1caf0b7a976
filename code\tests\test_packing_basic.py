import math

import pytest

from packing.protocols import Box3<PERSON>, <PERSON><PERSON>, Po<PERSON>, PlacedBox
from packing.heuristics import NeighborCornersHeuristic
from packing.scores.height_score import HeightScore
from packing.scores.gap_score import GapScore
from packing.scores.skew_score import SkewScore
from packing.simulator_bullet import BulletPhysicsSimulator
from packing.packer import Packer
from packing.constraints import (
    InsideCrateConstraint,
    NoOverlapConstraint,
    VerticalPathClearConstraint,
)


def make_crate():
    return Crate(width=0.6, depth=0.4, max_height=0.6)


def test_pack_single_box():
    crate = make_crate()
    sim = BulletPhysicsSimulator()
    placement = NeighborCornersHeuristic(neighbors=0, distance_between=0.015)
    scores = [HeightScore(weight=1.0)]
    packer = Packer(crate, sim, placement, scores, buffer_size=1)

    boxes = [Box3D(width=0.2, depth=0.2, height=0.1)]
    result = packer.pack_stream(boxes)
    assert len(result.placements) == 1
    pb = result.placements[0]
    assert 0.0 <= pb.pose.x <= crate.width - pb.box.width
    assert 0.0 <= pb.pose.y <= crate.depth - pb.box.depth
    assert (pb.pose.z + pb.box.height) <= crate.max_height + 1e-6


def test_no_overlap_two_boxes():
    crate = make_crate()
    sim = BulletPhysicsSimulator()
    placement = NeighborCornersHeuristic(neighbors=0)
    scores = [HeightScore(weight=1.0), GapScore(weight=0.0)]  # disable gap bias
    packer = Packer(crate, sim, placement, scores, buffer_size=2)

    boxes = [
        Box3D(width=0.3, depth=0.2, height=0.1),
        Box3D(width=0.3, depth=0.2, height=0.1),
    ]
    result = packer.pack_stream(boxes)
    assert len(result.placements) == 2
    a, b = result.placements
    # Axis-aligned no overlap check with small epsilon tolerance
    eps = 1e-3

    def aabb_overlap_eps(a, b, eps):
        return (
            (a.pose.x < b.pose.x + b.box.width - eps)
            and (b.pose.x < a.pose.x + a.box.width - eps)
            and (a.pose.y < b.pose.y + b.box.depth - eps)
            and (b.pose.y < a.pose.y + a.box.depth - eps)
            and (a.pose.z < b.pose.z + b.box.height - eps)
            and (b.pose.z < a.pose.z + a.box.height - eps)
        )

    assert not aabb_overlap_eps(a, b, eps)


def test_height_limit_rejects():
    crate = make_crate()
    sim = BulletPhysicsSimulator()
    placement = NeighborCornersHeuristic(neighbors=0)
    scores = [HeightScore(weight=1.0)]
    packer = Packer(crate, sim, placement, scores, buffer_size=1)

    # Box too tall
    boxes = [Box3D(width=0.5, depth=0.5, height=0.7)]
    result = packer.pack_stream(boxes)
    assert len(result.placements) == 0
