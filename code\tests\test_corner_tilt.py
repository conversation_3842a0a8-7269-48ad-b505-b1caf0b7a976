import math
from time import sleep

import pytest

from packing.protocols import Box3<PERSON>, <PERSON><PERSON>, Po<PERSON>, PlacedBox
from packing.simulator_bullet import BulletPhysicsSimulator


@pytest.mark.gui
def test_corner_partial_support_causes_tilt():
    # Visualizer and packer for debugging
    from packing.visualizer_o3d import Open3DVisualizer
    from packing.heuristics import NeighborCornersHeuristic
    from packing.scores.height_score import HeightScore
    from packing.packer import Packer

    # Crate with enough room
    crate = Crate(0.6, 0.6, 0.6)
    sim = BulletPhysicsSimulator(use_gui=True)

    # Prepare packer (we'll use it to hold state and visualize)
    placement = NeighborCornersHeuristic(neighbors=0, distance_between=0.015)
    scores = [HeightScore(weight=1.0)]
    packer = Packer(crate, sim, placement, scores, buffer_size=1)

    # Place a small base box exactly in the crate's origin corner and reset world
    base = PlacedBox(Box3D(0.10, 0.10, 0.10), Pose(0.0, 0.0, 0.0))
    sim.world_reset(crate, [base])
    packer.placed = [base]

    # Visualize initial state
    viz = Open3DVisualizer(show_window=True, update_delay_s=0.3)
    viz.update(crate, packer.placed)

    # Drop a larger box aligned to the same corner and starting at the base's top z
    # This should be only partially supported and tilt until the far side touches the floor
    top = PlacedBox(Box3D(0.30, 0.30, 0.20), Pose(0.05, 0.0, base.box.height))

    settled = sim.simulate_drop(crate, top)
    assert settled is not None
    print(top.pose)
    print(settled.pose)
    assert top.pose != settled.pose

    # Commit to world and packer state, then visualize
    sim.place_static(crate, settled)
    packer.placed.append(settled)
    viz.update(crate, packer.placed)

    # Inspect Bullet's last Euler angles from the simulation to detect roll/pitch
    metrics = sim.candidate_metrics()
    assert metrics is not None
    roll, pitch, yaw = metrics["euler"]

    # Expect at least a modest tilt around x or y (>= ~2 degrees)
    assert (abs(roll) >= math.radians(2.0)) or (abs(pitch) >= math.radians(2.0))

    viz.block_until_closed(packer.crate, packer.placed)


if __name__ == "__main__":
    test_corner_partial_support_causes_tilt()
