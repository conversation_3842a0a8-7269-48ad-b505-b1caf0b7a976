from dataclasses import dataclass
from typing import Iterable, <PERSON><PERSON>
import math

import numpy as np
import pybullet as p

from ..protocols import Box3D, Crate, PlacedBox, ScoringHeuristicProtocol, PhysicsSimulatorProtocol


@dataclass
class OverhangScore(ScoringHeuristicProtocol):
    """Compute overhang score based on ray casting under the settled box.

    Uses a grid of rays cast downward from the bottom face of the box to measure
    distances to floor/supporting boxes. Overhang volume = sum(distances) * grid_cell_area.
    Score = -weight * (overhang_volume / bottom_surface_area).
    """

    weight: float = 10.0
    grid_resolution: float = 0.02  # grid spacing in meters

    def _grid_points_and_cell(
        self, simulator: PhysicsSimulatorProtocol, candidate: PlacedBox
    ) -> Tuple[list[tuple[float, float, float]], int, int, float]:
        """Build the ray-cast grid under the candidate bottom face.
        Returns (points, nx, ny, cell_area).
        """
        # Use settled pose and, if available, full orientation from metrics
        metrics = getattr(simulator, "candidate_metrics", lambda: None)()
        box = candidate.box
        pose = candidate.pose
        grid_points: list[tuple[float, float, float]] = []

        if metrics is not None and "center" in metrics and "euler" in metrics:
            cx, cy, cz = metrics["center"]
            roll, pitch, yaw = metrics["euler"]
            # Local half-extents
            hx, hy, hz = box.width / 2.0, box.depth / 2.0, box.height / 2.0
            # Rotation matrix Rz(yaw)*Ry(pitch)*Rx(roll)
            cr, sr = math.cos(roll), math.sin(roll)
            cp, sp = math.cos(pitch), math.sin(pitch)
            cyw, syw = math.cos(yaw), math.sin(yaw)
            # Combined rotation matrix
            R = (
                (cyw * cp, cyw * sp * sr - syw * cr, cyw * sp * cr + syw * sr),
                (syw * cp, syw * sp * sr + cyw * cr, syw * sp * cr - cyw * sr),
                (-sp, cp * sr, cp * cr),
            )
            # Grid in local XY on bottom face at z = -hz
            nx = max(1, int((2 * hx) / self.grid_resolution))
            ny = max(1, int((2 * hy) / self.grid_resolution))
            cell_area = (2 * hx / nx) * (2 * hy / ny)
            for i in range(nx):
                for j in range(ny):
                    lx = -hx + (i + 0.5) * (2 * hx / nx)
                    ly = -hy + (j + 0.5) * (2 * hy / ny)
                    lz = -hz
                    # Rotate to world
                    wx = cx + R[0][0] * lx + R[0][1] * ly + R[0][2] * lz
                    wy = cy + R[1][0] * lx + R[1][1] * ly + R[1][2] * lz
                    wz = cz + R[2][0] * lx + R[2][1] * ly + R[2][2] * lz
                    grid_points.append((wx, wy, wz))
        return grid_points, nx, ny, cell_area

    def overhang_distance_map(
        self,
        crate: Crate,
        simulator: PhysicsSimulatorProtocol,
        candidate: PlacedBox,
    ) -> tuple[np.ndarray, float]:
        """Compute a 2D map (ny x nx) of downward distances under the candidate.
        Returns (distances_m, cell_area_m2).
        """
        # Get the simulator's internal client for ray casting
        if not hasattr(simulator, "_client") or simulator._client is None:
            return np.zeros((1, 1), dtype=np.float32), 0.0

        grid_points, nx, ny, cell_area = self._grid_points_and_cell(simulator, candidate)
        if not grid_points:
            return np.zeros((1, 1), dtype=np.float32), 0.0

        # Cast rays downward from each grid point
        ray_starts = [(x, y, z) for x, y, z in grid_points]
        ray_ends = [(x, y, z - 10.0) for x, y, z in grid_points]
        ray_results = p.rayTestBatch(ray_starts, ray_ends, physicsClientId=simulator._client)

        distances: list[float] = []
        for i, result in enumerate(ray_results):
            hit_object_id, link_index, hit_fraction, hit_position, hit_normal = result
            if hit_object_id != -1:
                distances.append(float(hit_fraction) * 10.0)
            else:
                distances.append(10.0)
        # Note: grid was filled with i looping over nx outside and j over ny inside ->
        # distances list is in (i-major, j-minor) order; reshape accordingly to (ny, nx)
        dist_map = np.array(distances, dtype=np.float32).reshape((nx, ny)).T
        return dist_map, float(cell_area)

    def score(
        self,
        crate: Crate,
        simulator: PhysicsSimulatorProtocol,
        candidate: PlacedBox,
        placed: Iterable[PlacedBox],
    ) -> float:
        # Get the simulator's internal client for ray casting
        if not hasattr(simulator, "_client") or simulator._client is None:
            return 0.0  # No simulation context available

        # Build grid and cast
        grid_points, nx, ny, cell_area = self._grid_points_and_cell(simulator, candidate)
        if not grid_points:
            return 0.0
        ray_starts = [(x, y, z) for x, y, z in grid_points]
        ray_ends = [(x, y, z - 10.0) for x, y, z in grid_points]
        ray_results = p.rayTestBatch(ray_starts, ray_ends, physicsClientId=simulator._client)

        total_overhang_volume = 0.0
        for i, result in enumerate(ray_results):
            hit_object_id, link_index, hit_fraction, hit_position, hit_normal = result
            if hit_object_id != -1:  # Hit something
                distance = float(hit_fraction) * 10.0  # Distance to hit point
                total_overhang_volume += distance * cell_area
            else:
                # No hit - ray went to infinity, use maximum distance
                total_overhang_volume += 10.0 * cell_area

        # Normalize by bottom surface area
        box = candidate.box
        bottom_surface_area = box.width * box.depth
        if bottom_surface_area <= 0:
            return 0.0

        overhang_ratio = total_overhang_volume / bottom_surface_area
        return -self.weight * overhang_ratio

    def render_overhang_heatmap(
        self,
        crate: Crate,
        simulator: PhysicsSimulatorProtocol,
        candidate: PlacedBox,
        show: bool = False,
        window_name: str = "Overhang Heatmap",
    ) -> "np.ndarray":
        """Generate a colored heatmap image from the overhang distance map.
        Returns a BGR image (uint8). If show=True, displays it with OpenCV.
        """
        dist_map, _ = self.overhang_distance_map(crate, simulator, candidate)
        # Normalize distances to 0..1 for visualization (cap at 1m for clarity)
        if dist_map.size == 0:
            return np.zeros((1, 1, 3), dtype=np.uint8)
        cap = max(1e-6, float(np.percentile(dist_map, 99.0)))  # robust cap
        norm = np.clip(dist_map / cap, 0.0, 1.0)
        img = (norm * 255.0).astype(np.uint8)
        try:
            import cv2 as cv

            color = cv.applyColorMap(img, cv.COLORMAP_INFERNO)
            if show:
                cv.imshow(window_name, color)
                cv.waitKey(1)
            return color
        except Exception:
            # If OpenCV not available at runtime, return grayscale 3-channel
            return np.repeat(img[..., None], 3, axis=2)
