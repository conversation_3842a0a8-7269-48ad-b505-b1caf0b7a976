import pytest

from packing.protocols import Box3<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, PlacedBox
from packing.simulator_bullet import BulletPhysicsSimulator
from packing.visualizer_o3d import Open3DVisualizer


@pytest.mark.gui
def test_overhang_visual_debug():
    crate = Crate(0.6, 0.6, 0.6)
    sim = BulletPhysicsSimulator(use_gui=True)
    base = PlacedBox(Box3D(0.3, 0.3, 0.1), Po<PERSON>(0.0, 0.0, 0.0))
    sim.world_reset(crate, [base])

    cand = PlacedBox(Box3D(0.3, 0.3, 0.1), Pose.from_yaw(0.2, 0.05, 0.1, 0.2))
    settled = sim.simulate_drop(crate, cand)
    assert settled is not None
    assert settled.settled
    print(settled.pose.r.as_euler("zxy", degrees=True))

    viz = Open3DVisualizer(show_window=True, update_delay_s=0.5)
    viz.update(crate, [base, settled])
    viz.block_until_closed(crate, [base, settled])


if __name__ == "__main__":
    test_overhang_visual_debug()
