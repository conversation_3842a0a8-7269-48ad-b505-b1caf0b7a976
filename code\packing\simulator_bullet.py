from __future__ import annotations

from dataclasses import dataclass, field
from typing import Dict, Iterable, Optional, Tuple

import math
import random

import pybullet as p
import pybullet_data

from .protocols import (
    Box3D,
    Pose,
    PlacedBox,
    Crate,
    PhysicsSimulatorProtocol,
    get_effective_footprint_dims,
)


@dataclass
class _BodyInfo:
    body_id: int
    placed: PlacedBox


@dataclass
class BulletPhysicsSimulator(PhysicsSimulatorProtocol):
    """PyBullet-based simulator implementing the PhysicsSimulatorProtocol.

    Notes:
    - Uses a single pybullet DIRECT connection for performance.
    - Placed boxes are instantiated as static bodies (baseMass=0) to speed up simulation.
    - Candidate simulation uses a temporary dynamic body which is removed afterwards.
    - Coordinates: pose.x/pose.y/pose.z is the bottom-left-lower corner of the box footprint.
    - Only yaw (Z rotation) is considered; roll/pitch are expected to be ~0 after settling.
    """

    time_step: float = 1.0 / 120.0
    gravity_z: float = -5.0
    drop_mass: float = 5.0
    stabilize_lin_vel: float = 0.01
    stabilize_ang_vel: float = 0.01
    max_steps_default: int = 360000
    start_clearance: float = 0.05
    wall_margin: float = 0.10001

    _client: Optional[int] = field(default=None, init=False)
    use_gui: bool = False
    _crate: Optional[Crate] = field(default=None, init=False)
    _static_bodies: Dict[int, _BodyInfo] = field(default_factory=dict, init=False)
    _walls: Tuple[int, int, int, int] | None = field(default=None, init=False)
    _last_metrics: Optional[dict] = field(default=None, init=False)

    # Public API -----------------------------------------------------------------

    def world_reset(self, crate: Crate, placed: Iterable[PlacedBox]) -> None:
        self._disconnect()
        self._connect()
        self._crate = crate
        self._setup_world(crate)
        self._static_bodies.clear()
        for pb in placed:
            self.place_static(crate, pb)
        self._last_metrics = None

    def simulate_drop(
        self, crate: Crate, candidate: PlacedBox, max_steps: int | None = None
    ) -> Optional[PlacedBox]:
        assert self._client is not None, "Simulator not initialized. Call world_reset first."
        max_steps = max_steps or self.max_steps_default
        # Create dynamic body above the target location
        w, d, h = candidate.box.width, candidate.box.depth, candidate.box.height
        cx = candidate.pose.x + w / 2.0
        cy = candidate.pose.y + d / 2.0
        # Start slightly above intended bottom z to avoid unnecessary settling travel.
        target_bottom_z = max(
            0.0,
            candidate.pose.z,
        )
        start_z = target_bottom_z + h / 2.0 + self.start_clearance
        start_pos = (cx, cy, start_z)
        # Use candidate's orientation (full quaternion if provided)
        quat = candidate.pose.quat
        half_ext = (w / 2.0, d / 2.0, h / 2.0)

        col_id = p.createCollisionShape(p.GEOM_BOX, halfExtents=half_ext)
        vis_id = p.createVisualShape(
            p.GEOM_BOX,
            halfExtents=half_ext,
            rgbaColor=[random.random(), random.random(), random.random(), 1],
        )
        body_id = p.createMultiBody(
            baseMass=self.drop_mass,
            baseCollisionShapeIndex=col_id,
            baseVisualShapeIndex=vis_id,
            basePosition=start_pos,
            baseOrientation=quat,
        )
        # Add damping and friction to help settle quickly and avoid long sliding
        p.changeDynamics(
            body_id,
            -1,
            linearDamping=0.04,
            angularDamping=0.04,
            lateralFriction=0.8,
            rollingFriction=0.001,
            spinningFriction=0.001,
        )

        # Step simulation until stabilized
        settled = False
        settle_steps = 10
        for _ in range(max_steps):
            p.stepSimulation()
            lin_vel, ang_vel = p.getBaseVelocity(body_id)
            if all(abs(v) < self.stabilize_lin_vel for v in lin_vel) and all(
                abs(v) < self.stabilize_ang_vel for v in ang_vel
            ):
                settle_steps -= 1
                if settle_steps <= 0:
                    settled = True
                    break
            else:
                settle_steps = 10

        pos, orn = p.getBasePositionAndOrientation(body_id)
        euler = p.getEulerFromQuaternion(orn)
        aabb_min, aabb_max = p.getAABB(body_id)

        final_pose = Pose.from_quat(x=pos[0], y=pos[1], z=pos[2], quat=orn)
        placed = PlacedBox(candidate.box, final_pose, settled)

        # Cache metrics for scoring
        self._last_metrics = self._compute_metrics(body_id, placed, aabb_min, aabb_max, euler)

        # Remove the dynamic body (we only need the pose/metrics)
        p.removeBody(body_id)

        # # Reject if outside crate bounds
        # if not self._inside_bounds(crate, placed):
        #     return None
        return placed

    def place_static(self, crate: Crate, placed: PlacedBox) -> None:
        assert self._client is not None, "Simulator not initialized. Call world_reset first."
        w, d, h = placed.box.width, placed.box.depth, placed.box.height
        cx = placed.pose.x + w / 2.0
        cy = placed.pose.y + d / 2.0
        cz = placed.pose.z + h / 2.0
        quat = placed.pose.quat
        half_ext = (w / 2.0, d / 2.0, h / 2.0)
        col_id = p.createCollisionShape(p.GEOM_BOX, halfExtents=half_ext)
        vis_id = p.createVisualShape(
            p.GEOM_BOX, halfExtents=half_ext, rgbaColor=[0.8, 0.5, 0.2, 1]
        )

        body_id = p.createMultiBody(
            baseMass=0.0,
            baseCollisionShapeIndex=col_id,
            baseVisualShapeIndex=vis_id,
            basePosition=(cx, cy, cz),
            baseOrientation=quat,
        )
        self._static_bodies[body_id] = _BodyInfo(body_id=body_id, placed=placed)

    def vertical_clearance_ok(self, crate: Crate, candidate: PlacedBox) -> bool:
        # Basic vertical path clearance: footprint AABB vs static boxes
        c_min_x, c_max_x = candidate.pose.x, candidate.pose.x + candidate.box.width
        c_min_y, c_max_y = candidate.pose.y, candidate.pose.y + candidate.box.depth
        c_bottom = candidate.pose.z
        for info in self._static_bodies.values():
            pb = info.placed
            p_min_x, p_max_x = pb.pose.x, pb.pose.x + pb.box.width
            p_min_y, p_max_y = pb.pose.y, pb.pose.y + pb.box.depth
            overlap_x = (c_min_x < p_max_x) and (p_min_x < c_max_x)
            overlap_y = (c_min_y < p_max_y) and (p_min_y < c_max_y)
            if overlap_x and overlap_y:
                p_top = pb.pose.z + pb.box.height
                if p_top > c_bottom + 1e-4:
                    return False
        return True

    # Optional helper for scoring ------------------------------------------------

    def candidate_metrics(self) -> Optional[dict]:
        """Return metrics of the most recent simulate_drop for scoring purposes."""
        return self._last_metrics

    # Internal -------------------------------------------------------------------

    def _connect(self) -> None:
        self._client = p.connect(p.GUI if self.use_gui else p.DIRECT)
        p.setRealTimeSimulation(0)
        p.setTimeStep(self.time_step)
        p.setAdditionalSearchPath(pybullet_data.getDataPath())
        p.setGravity(0, 0, self.gravity_z)

    def _disconnect(self) -> None:
        try:
            if self._client is not None:
                p.disconnect(self._client)
        finally:
            self._client = None

    def _setup_world(self, crate: Crate) -> None:
        # Plane
        p.loadURDF("plane.urdf")
        # Walls (invisible)
        w, d, max_h = crate.width, crate.depth, crate.max_height
        base = 0.0
        # left and right walls along X
        walls = []
        walls.append(
            self._create_wall(
                center=(-self.wall_margin, d / 2.0, base), half_ext=(0.1, d, 2 * max_h)
            )
        )
        walls.append(
            self._create_wall(
                center=(w + self.wall_margin, d / 2.0, base), half_ext=(0.1, d, 2 * max_h)
            )
        )
        # front/back walls along Y
        walls.append(
            self._create_wall(
                center=(w / 2.0, d + self.wall_margin, base), half_ext=(w, 0.1, 2 * max_h)
            )
        )
        walls.append(
            self._create_wall(
                center=(w / 2.0, -self.wall_margin, base), half_ext=(w, 0.1, 2 * max_h)
            )
        )
        self._walls = tuple(walls)  # type: ignore
        # Pallet (as base platform)
        half = (w / 2.0, d / 2.0, base / 2.0)
        col = p.createCollisionShape(p.GEOM_BOX, halfExtents=half)
        vis = p.createVisualShape(p.GEOM_BOX, halfExtents=half, rgbaColor=[0.6, 0.6, 0.6, 1])
        p.createMultiBody(
            baseMass=0.0,
            baseCollisionShapeIndex=col,
            baseVisualShapeIndex=vis,
            basePosition=(w / 2.0, d / 2.0, base / 2.0),
            baseOrientation=[0, 0, 0, 1],
        )

    def _create_wall(
        self, center: Tuple[float, float, float], half_ext: Tuple[float, float, float]
    ) -> int:
        col = p.createCollisionShape(p.GEOM_BOX, halfExtents=half_ext)
        vis = p.createVisualShape(p.GEOM_BOX, halfExtents=half_ext, rgbaColor=[0, 0, 0, 0])
        return p.createMultiBody(
            baseMass=0.0,
            baseCollisionShapeIndex=col,
            baseVisualShapeIndex=vis,
            basePosition=center,
            baseOrientation=[0, 0, 0, 1],
        )

    def _inside_bounds(self, crate: Crate, placed: PlacedBox) -> bool:
        x_ok = 0.0 <= placed.pose.x <= crate.width - placed.box.width + 1e-6
        y_ok = 0.0 <= placed.pose.y <= crate.depth - placed.box.depth + 1e-6
        z_ok = (placed.pose.z + placed.box.height) <= crate.max_height + 1e-6
        return x_ok and y_ok and z_ok

    def _compute_metrics(self, body_id: int, placed: PlacedBox, aabb_min, aabb_max, euler) -> dict:
        # Distances to nearest obstacles/walls in +X, -X, +Y, -Y directions measured from center
        cx = placed.pose.x + placed.box.width / 2.0
        cy = placed.pose.y + placed.box.depth / 2.0
        cz = placed.pose.z + placed.box.height / 2.0
        start = (cx, cy, cz)
        ends = [
            (cx + 10, cy, cz),
            (cx - 10, cy, cz),
            (cx, cy + 10, cz),
            (cx, cy - 10, cz),
        ]
        distances = []
        for end in ends:
            ray = p.rayTest(start, end)
            if ray and ray[0][0] != -1:
                hit_pos = ray[0][3]
                dist = math.dist(start, hit_pos)
            else:
                dist = 0.0
            distances.append(dist)
        # subtract half extents so distances are from the box faces
        half_w, half_d = placed.box.width / 2.0, placed.box.depth / 2.0
        face_dist = (
            distances[0] - half_w,
            distances[1] - half_w,
            distances[2] - half_d,
            distances[3] - half_d,
        )
        return {
            "euler": euler,
            "aabb_min": aabb_min,
            "aabb_max": aabb_max,
            "center": (cx, cy, cz),
            "face_dist": face_dist,
        }
