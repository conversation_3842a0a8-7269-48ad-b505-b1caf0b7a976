import pytest

from packing.protocols import Box3<PERSON>, <PERSON><PERSON>, PlacedBox, Crate
from packing.simulator_bullet import BulletPhysicsSimulator


def test_simulate_drop_basic():
    crate = Crate(0.6, 0.4, 0.6)
    sim = BulletPhysicsSimulator()
    sim.world_reset(crate, [])
    b = Box3D(0.2, 0.2, 0.1)
    cand = PlacedBox(b, Pose(0.0, 0.0, 0.0))
    settled = sim.simulate_drop(crate, cand)
    assert settled is not None
    assert 0.0 <= settled.pose.x <= crate.width - b.width
    assert 0.0 <= settled.pose.y <= crate.depth - b.depth
    assert (settled.pose.z + b.height) <= crate.max_height + 1e-6
