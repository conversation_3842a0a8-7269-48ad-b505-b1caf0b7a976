from packing.protocols import Box3D, Crate
from packing.heuristics import NeighborCornersHeuristic
from packing.scores.height_score import HeightScore
from packing.simulator_bullet import BulletPhysicsSimulator
from packing.packer import Packer


def test_second_box_stacks_on_first_when_no_xy_room():
    # Crate small enough to force stacking (no horizontal room for two 0.3x0.3 boxes)
    crate = Crate(width=0.3, depth=0.3, max_height=0.5)
    sim = BulletPhysicsSimulator()
    placement = NeighborCornersHeuristic(neighbors=0)
    scores = [HeightScore(weight=1.0)]
    packer = Packer(crate, sim, placement, scores, buffer_size=1)

    boxes = [
        Box3D(width=0.3, depth=0.3, height=0.1),
        Box3D(width=0.3, depth=0.3, height=0.1),
    ]
    result = packer.pack_stream(boxes)
    assert len(result.placements) == 2
    a, b = result.placements
    # First should be on the ground
    assert abs(a.pose.z - 0.0) < 1e-3
    # Second should be stacked on top of first (within small epsilon)
    assert b.pose.z >= a.pose.z + a.box.height - 2e-3
