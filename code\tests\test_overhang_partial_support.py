from packing.protocols import Box3<PERSON>, <PERSON><PERSON>, PlacedBox
from packing.scores.overhang_score import OverhangScore


def test_second_box_partial_on_first_and_floor(crate, sim):
    # Place a base box smaller than the top box footprint so the top rests partly on it and partly on the floor
    base = PlacedBox(Box3D(0.2, 0.3, 0.1), Pose(0.15, 0.15, 0.0))
    sim.world_reset(crate, [base])

    # Candidate larger in width than base, positioned to overhang and touch floor
    top = PlacedBox(Box3D(0.3, 0.3, 0.1), <PERSON><PERSON>(0.10, 0.15, 0.1))
    settled = sim.simulate_drop(crate, top)
    assert settled is not None

    # Score should be negative (penalty) but not extreme; just check it's < 0
    s = OverhangScore(weight=1.0, grid_resolution=0.05).score(crate, sim, settled, [base])
    assert s < 0.0
